import React, { useRef, useEffect } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { createProceduralMaterial, updateProceduralMaterial } from '../../shaders/proceduralMaterial';
import { MaterialSettings } from '../custom-material-panel/custom-material-panel';

interface ProceduralMaterialProps {
  settings: MaterialSettings;
  geometry?: THREE.BufferGeometry;
}

export const ProceduralMaterial: React.FC<ProceduralMaterialProps> = ({ 
  settings, 
  geometry 
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const materialRef = useRef<THREE.ShaderMaterial | null>(null);

  // 创建材质
  useEffect(() => {
    if (materialRef.current) {
      materialRef.current.dispose();
    }

    materialRef.current = createProceduralMaterial({
      color: settings.color,
      metalness: settings.metalness,
      roughness: settings.roughness,
      opacity: settings.opacity,
      useProceduralTexture: settings.useProceduralTexture,
      proceduralType: settings.proceduralType,
      noiseScale: settings.noiseScale,
      noiseIntensity: settings.noiseIntensity,
      noiseOctaves: settings.noiseOctaves,
      noiseFrequency: settings.noiseFrequency,
      noiseAmplitude: settings.noiseAmplitude,
      animationSpeed: settings.animationSpeed
    });

    if (meshRef.current) {
      meshRef.current.material = materialRef.current;
    }

    return () => {
      if (materialRef.current) {
        materialRef.current.dispose();
      }
    };
  }, []); // 只在组件挂载时创建一次

  // 更新材质参数
  useEffect(() => {
    if (materialRef.current) {
      updateProceduralMaterial(materialRef.current, {
        color: settings.color,
        metalness: settings.metalness,
        roughness: settings.roughness,
        opacity: settings.opacity,
        useProceduralTexture: settings.useProceduralTexture,
        proceduralType: settings.proceduralType,
        noiseScale: settings.noiseScale,
        noiseIntensity: settings.noiseIntensity,
        noiseOctaves: settings.noiseOctaves,
        noiseFrequency: settings.noiseFrequency,
        noiseAmplitude: settings.noiseAmplitude,
        animationSpeed: settings.animationSpeed
      });
    }
  }, [settings]);

  // 动画更新
  useFrame((state) => {
    if (materialRef.current && settings.animationSpeed && settings.animationSpeed > 0) {
      materialRef.current.uniforms.time.value = state.clock.elapsedTime;
    }
  });

  return (
    <mesh ref={meshRef} geometry={geometry}>
      {/* 材质会通过 ref 设置 */}
    </mesh>
  );
};

// 用于材质缩略图的球体组件
export const ProceduralMaterialSphere: React.FC<{ settings: MaterialSettings }> = ({ settings }) => {
  const sphereGeometry = new THREE.SphereGeometry(1, 64, 64);

  return <ProceduralMaterial settings={settings} geometry={sphereGeometry} />;
};

export default ProceduralMaterial;
