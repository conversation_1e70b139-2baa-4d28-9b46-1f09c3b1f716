import React, { useState } from 'react';
import './input-box.css';

interface InputBoxProps {
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  disabled?: boolean;
  width?: number;
  fullWidth?: boolean;
  type?: 'text' | 'password' | 'number' | 'email' | 'tel';
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
}

export const InputBox: React.FC<InputBoxProps> = ({
  placeholder = '',
  value,
  defaultValue = '',
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  width,
  fullWidth = false,
  type = 'text',
  prefixIcon,
  suffixIcon,
  size = 'medium'
}) => {
  const [inputValue, setInputValue] = useState(value !== undefined ? value : defaultValue);
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange?.(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const controlledValue = value !== undefined ? value : inputValue;

  return (
    <div 
      className={`input-box-container ${isFocused ? 'focused' : ''} ${disabled ? 'disabled' : ''} ${size}`}
      style={{
        width: fullWidth ? '100%' : width ? `${width}px` : '240px',
      }}
    >
      {prefixIcon && (
        <div className="input-prefix-icon">
          {prefixIcon}
        </div>
      )}
      
      <input
        type={type}
        value={controlledValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        className={`input-box ${prefixIcon ? 'has-prefix' : ''} ${suffixIcon ? 'has-suffix' : ''}`}
      />
      
      {suffixIcon && (
        <div className="input-suffix-icon">
          {suffixIcon}
        </div>
      )}
    </div>
  );
};
