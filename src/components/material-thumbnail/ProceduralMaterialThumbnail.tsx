import React, { memo, useCallback, useRef, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import type { MaterialSettings } from '../../types/material';
import { ProceduralMaterialSphere } from '../procedural-material/ProceduralMaterial';
import './material-thumbnail.css';

interface ProceduralMaterialThumbnailProps {
  settings: MaterialSettings;
  active?: boolean;
  onClick?: () => void;
  size?: 'default' | 'large';
}

/**
 * 程序化材质缩略图（3D球体）
 * 使用 ShaderMaterial 显示程序化纹理效果
 */
const ProceduralMaterialThumbnail: React.FC<ProceduralMaterialThumbnailProps> = ({ 
  settings, 
  active = false, 
  onClick, 
  size = 'default' 
}) => {
  const invalidateRef = useRef<(() => void) | null>(null);

  const handleCreated = useCallback(({ invalidate }: { invalidate: () => void }) => {
    // 保存 invalidate，在材质准备好后调用一次使其渲染首帧
    invalidateRef.current = invalidate;
  }, []);

  // 当材质属性变化时，触发重新渲染
  useEffect(() => {
    if (invalidateRef.current) {
      invalidateRef.current();
    }
  }, [
    settings.color,
    settings.metalness,
    settings.roughness,
    settings.opacity,
    settings.useProceduralTexture,
    settings.proceduralType,
    settings.noiseScale,
    settings.noiseIntensity,
    settings.noiseOctaves,
    settings.noiseFrequency,
    settings.noiseAmplitude,
    settings.animationSpeed
  ]);

  return (
    <div
      className={`material-item${active ? ' active' : ''} ${size === 'large' ? 'material-item-large' : ''}`}
      onClick={onClick}
    >
      <div className="thumbnail-canvas">
        <Canvas
          frameloop={settings.animationSpeed && settings.animationSpeed > 0 ? "always" : "demand"}
          camera={{ position: [0, 0, 1.5], fov: 45 }}
          onCreated={handleCreated}
          gl={{ antialias: true, alpha: true }}
        >
          {/* 环境光/主光源 */}
          <ambientLight intensity={0.7} />
          <directionalLight position={[5, 5, 5]} intensity={1} />

          {/* 程序化材质球 */}
          <ProceduralMaterialSphere 
            settings={settings}
          />

          {/* 使用城市 HDR 环境 */}
          <Environment preset="city" />
        </Canvas>
      </div>
    </div>
  );
};

export default memo(ProceduralMaterialThumbnail, (prevProps, nextProps) => {
  // 比较所有相关属性
  const prevSettings = prevProps.settings;
  const nextSettings = nextProps.settings;
  
  return (
    prevSettings.color === nextSettings.color &&
    prevSettings.metalness === nextSettings.metalness &&
    prevSettings.roughness === nextSettings.roughness &&
    prevSettings.opacity === nextSettings.opacity &&
    prevSettings.useProceduralTexture === nextSettings.useProceduralTexture &&
    prevSettings.proceduralType === nextSettings.proceduralType &&
    prevSettings.noiseScale === nextSettings.noiseScale &&
    prevSettings.noiseIntensity === nextSettings.noiseIntensity &&
    prevSettings.noiseOctaves === nextSettings.noiseOctaves &&
    prevSettings.noiseFrequency === nextSettings.noiseFrequency &&
    prevSettings.noiseAmplitude === nextSettings.noiseAmplitude &&
    prevSettings.animationSpeed === nextSettings.animationSpeed &&
    prevProps.active === nextProps.active &&
    prevProps.size === nextProps.size
  );
});
