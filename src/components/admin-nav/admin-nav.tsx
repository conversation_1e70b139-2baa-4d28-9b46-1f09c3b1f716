import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Settings } from 'lucide-react';
import './admin-nav.css';

export const AdminNav: React.FC = () => {
  const location = useLocation();
  const isAdmin = location.pathname.includes('/admin');
  
  return (
    <div className="admin-nav">
      {isAdmin ? (
        <Link to="/" className="nav-link">
          <Home size={16} />
          <span>前台页面</span>
        </Link>
      ) : (
        <Link to="/admin" className="nav-link">
          <Settings size={16} />
          <span>后台管理</span>
        </Link>
      )}
    </div>
  );
};
