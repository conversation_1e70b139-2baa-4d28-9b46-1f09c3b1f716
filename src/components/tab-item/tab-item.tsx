import React from 'react';
import type { LucideIcon } from 'lucide-react';
import './tab-item.css';

export interface TabItemProps {
  /** 标签文本 */
  label: string;
  /** 图标组件 */
  icon?: LucideIcon;
  /** 是否选中 */
  isActive?: boolean;
  /** 点击事件处理函数 */
  onClick?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 自定义宽度 */
  width?: number | string;
}

export const TabItem: React.FC<TabItemProps> = ({
  label,
  icon: Icon,
  isActive = false,
  onClick,
  className = '',
  width = 104,
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // 计算样式
  const tabItemStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
  };

  // 选中和非选中状态使用不同的文本颜色
  const textColorClass = isActive ? 'tab-item__text--active' : 'tab-item__text--default';
  const iconColorClass = isActive ? 'tab-item__icon--active' : 'tab-item__icon--default';
  
  return (
    <div 
      className={`tab-item ${isActive ? 'tab-item--active' : 'tab-item--default'} ${className}`}
      onClick={handleClick}
      style={tabItemStyle}
      data-layer="tab-item"
    >
      {Icon && (
        <div className={`tab-item__icon-container ${iconColorClass}`}>
          <Icon size={16} />
        </div>
      )}
      <div className={`tab-item__text ${textColorClass}`}>{label}</div>
    </div>
  );
};
