import React, { useState } from 'react';
import { Pipette } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';
import type { MaterialSettings } from '../../types/material';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings;
}

// 重新导出类型以保持向后兼容性
export type { MaterialSettings } from '../../types/material';

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({
  onChange,
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness ?? 0.5;
  const defaultRoughness = defaultSettings?.roughness ?? 0.5;
  const defaultOpacity = defaultSettings?.opacity ?? 1;
  const defaultUseProceduralTexture = defaultSettings?.useProceduralTexture ?? false;
  const defaultProceduralType = defaultSettings?.proceduralType || 'noise';
  const defaultNoiseScale = defaultSettings?.noiseScale ?? 5.0;
  const defaultNoiseIntensity = defaultSettings?.noiseIntensity ?? 0.5;
  const defaultNoiseOctaves = defaultSettings?.noiseOctaves ?? 4;
  const defaultNoiseFrequency = defaultSettings?.noiseFrequency ?? 1.0;
  const defaultNoiseAmplitude = defaultSettings?.noiseAmplitude ?? 1.0;
  const defaultAnimationSpeed = defaultSettings?.animationSpeed ?? 0.0;

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [opacity, setOpacity] = useState<number>(defaultOpacity);
  const [useProceduralTexture, setUseProceduralTexture] = useState<boolean>(defaultUseProceduralTexture);
  const [proceduralType, setProceduralType] = useState<'noise' | 'marble' | 'wood' | 'cellular' | 'fbm'>(defaultProceduralType as 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm');
  const [noiseScale, setNoiseScale] = useState<number>(defaultNoiseScale);
  const [noiseIntensity, setNoiseIntensity] = useState<number>(defaultNoiseIntensity);
  const [noiseOctaves, setNoiseOctaves] = useState<number>(defaultNoiseOctaves);
  const [noiseFrequency, setNoiseFrequency] = useState<number>(defaultNoiseFrequency);
  const [noiseAmplitude, setNoiseAmplitude] = useState<number>(defaultNoiseAmplitude);
  const [animationSpeed, setAnimationSpeed] = useState<number>(defaultAnimationSpeed);

  // 更新材质设置
  const updateSettings = (updates: Partial<MaterialSettings> = {}) => {
    const updatedSettings: MaterialSettings = {
      color: updates.color ?? color,
      metalness: updates.metalness ?? metalness,
      roughness: updates.roughness ?? roughness,
      opacity: updates.opacity ?? opacity,
      useProceduralTexture: updates.useProceduralTexture ?? useProceduralTexture,
      proceduralType: updates.proceduralType ?? proceduralType,
      noiseScale: updates.noiseScale ?? noiseScale,
      noiseIntensity: updates.noiseIntensity ?? noiseIntensity,
      noiseOctaves: updates.noiseOctaves ?? noiseOctaves,
      noiseFrequency: updates.noiseFrequency ?? noiseFrequency,
      noiseAmplitude: updates.noiseAmplitude ?? noiseAmplitude,
      animationSpeed: updates.animationSpeed ?? animationSpeed,
    };

    onChange?.(updatedSettings);
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings({ color: newColor });
  };

  // 使用吸管工具
  const handleEyeDropper = async () => {
    if ('EyeDropper' in window) {
      try {
        // @ts-expect-error - EyeDropper API 可能不在所有TypeScript类型中
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();
        setColor(result.sRGBHex);
        updateSettings({ color: result.sRGBHex });
      } catch (error) {
        console.error('EyeDropper error:', error);
      }
    } else {
      console.warn('EyeDropper API not supported');
    }
  };

  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group column" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
          <Pipette 
            className="color-pipette-icon" 
            onClick={handleEyeDropper} 
            data-states="default" 
          />
        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings({ metalness: value });
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings({ roughness: value });
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="透明度">透明度</div>
        <Slider
          min={0}
          max={1}
          step={0.01}
          defaultValue={opacity}
          onChange={(value) => {
            setOpacity(value);
            updateSettings({ opacity: value });
          }}
          showValue={false}
          width="100%"
        />
      </div>

      {/* 程序化纹理开关 */}
      <div className="material-property-group" data-layer="Frame 38">
        <div className="property-label" data-layer="程序化纹理">程序化纹理</div>
        <label className="procedural-switch">
          <input
            type="checkbox"
            checked={useProceduralTexture}
            onChange={(e) => {
              setUseProceduralTexture(e.target.checked);
              updateSettings({ useProceduralTexture: e.target.checked });
            }}
          />
          <span className="switch-slider"></span>
        </label>
      </div>

      {/* 程序化纹理参数 */}
      {useProceduralTexture && (
        <>
          <div className="material-property-group" data-layer="Frame 39">
            <div className="property-label" data-layer="纹理类型">纹理类型</div>
            <select
              className="procedural-select"
              value={proceduralType}
              onChange={(e) => {
                const newType = e.target.value as 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm';
                setProceduralType(newType);
                updateSettings({ proceduralType: newType });
              }}
            >
              <option value="noise">噪声</option>
              <option value="marble">大理石</option>
              <option value="wood">木纹</option>
              <option value="cellular">细胞</option>
              <option value="fbm">分形噪声</option>
            </select>
          </div>

          <div className="material-property-group" data-layer="Frame 40">
            <div className="property-label" data-layer="缩放">缩放</div>
            <Slider
              min={0.1}
              max={20}
              step={0.1}
              defaultValue={noiseScale}
              onChange={(value) => {
                setNoiseScale(value);
                updateSettings({ noiseScale: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 41">
            <div className="property-label" data-layer="强度">强度</div>
            <Slider
              min={0}
              max={2}
              step={0.01}
              defaultValue={noiseIntensity}
              onChange={(value) => {
                setNoiseIntensity(value);
                updateSettings({ noiseIntensity: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 42">
            <div className="property-label" data-layer="细节层次">细节层次</div>
            <Slider
              min={1}
              max={8}
              step={1}
              defaultValue={noiseOctaves}
              onChange={(value) => {
                setNoiseOctaves(value);
                updateSettings({ noiseOctaves: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 43">
            <div className="property-label" data-layer="频率">频率</div>
            <Slider
              min={0.1}
              max={5}
              step={0.1}
              defaultValue={noiseFrequency}
              onChange={(value) => {
                setNoiseFrequency(value);
                updateSettings({ noiseFrequency: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 44">
            <div className="property-label" data-layer="振幅">振幅</div>
            <Slider
              min={0.1}
              max={3}
              step={0.1}
              defaultValue={noiseAmplitude}
              onChange={(value) => {
                setNoiseAmplitude(value);
                updateSettings({ noiseAmplitude: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>

          <div className="material-property-group" data-layer="Frame 45">
            <div className="property-label" data-layer="动画速度">动画速度</div>
            <Slider
              min={0}
              max={2}
              step={0.01}
              defaultValue={animationSpeed}
              onChange={(value) => {
                setAnimationSpeed(value);
                updateSettings({ animationSpeed: value });
              }}
              showValue={false}
              width="100%"
            />
          </div>
        </>
      )}
    </div>
  );
};