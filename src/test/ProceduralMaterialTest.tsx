import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment, OrbitControls } from '@react-three/drei';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import ProceduralMaterialThumbnail from '../components/material-thumbnail/ProceduralMaterialThumbnail';
import { ProceduralMaterialSphere } from '../components/procedural-material/ProceduralMaterial';
import type { MaterialSettings } from '../types/material';

/**
 * 程序化材质测试组件
 * 用于测试和演示程序化纹理功能
 */
const ProceduralMaterialTest: React.FC = () => {
  const [materialSettings, setMaterialSettings] = useState<MaterialSettings>({
    color: '#B39B9C',
    metalness: 0.5,
    roughness: 0.5,
    opacity: 1,
    useProceduralTexture: true,
    proceduralType: 'noise',
    noiseScale: 5.0,
    noiseIntensity: 0.5,
    noiseOctaves: 4,
    noiseFrequency: 1.0,
    noiseAmplitude: 1.0,
    animationSpeed: 0.1
  });

  const presetConfigs = [
    {
      name: '基础噪声',
      settings: {
        ...materialSettings,
        proceduralType: 'noise' as const,
        noiseScale: 5.0,
        noiseIntensity: 0.5,
        animationSpeed: 0.0
      }
    },
    {
      name: '大理石',
      settings: {
        ...materialSettings,
        proceduralType: 'marble' as const,
        color: '#F5F5DC',
        noiseScale: 3.0,
        noiseIntensity: 0.8,
        animationSpeed: 0.0
      }
    },
    {
      name: '木纹',
      settings: {
        ...materialSettings,
        proceduralType: 'wood' as const,
        color: '#8B4513',
        noiseScale: 8.0,
        noiseIntensity: 0.6,
        animationSpeed: 0.0
      }
    },
    {
      name: '细胞',
      settings: {
        ...materialSettings,
        proceduralType: 'cellular' as const,
        noiseScale: 10.0,
        noiseIntensity: 0.7,
        animationSpeed: 0.0
      }
    },
    {
      name: '分形噪声',
      settings: {
        ...materialSettings,
        proceduralType: 'fbm' as const,
        noiseScale: 4.0,
        noiseIntensity: 0.9,
        noiseOctaves: 6,
        animationSpeed: 0.0
      }
    },
    {
      name: '动态效果',
      settings: {
        ...materialSettings,
        proceduralType: 'noise' as const,
        noiseScale: 6.0,
        noiseIntensity: 0.8,
        animationSpeed: 0.5
      }
    }
  ];

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      display: 'flex',
      background: '#1A1A1A',
      color: '#fff'
    }}>
      {/* 3D 预览区域 */}
      <div style={{ flex: 1, position: 'relative' }}>
        <Canvas camera={{ position: [0, 0, 3], fov: 45 }}>
          <ambientLight intensity={0.5} />
          <directionalLight position={[5, 5, 5]} intensity={1} />
          
          <ProceduralMaterialSphere settings={materialSettings} />
          
          <Environment preset="city" />
          <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
        </Canvas>
        
        {/* 预设配置 */}
        <div style={{
          position: 'absolute',
          top: 20,
          left: 20,
          display: 'flex',
          flexDirection: 'column',
          gap: 10,
          background: 'rgba(0,0,0,0.8)',
          padding: 20,
          borderRadius: 8
        }}>
          <h3 style={{ margin: 0, marginBottom: 10 }}>预设配置</h3>
          {presetConfigs.map((preset, index) => (
            <button
              key={index}
              onClick={() => setMaterialSettings(preset.settings)}
              style={{
                padding: '8px 16px',
                background: '#2269EC',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer'
              }}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>
      
      {/* 控制面板 */}
      <div style={{ 
        width: 300, 
        padding: 20, 
        background: '#272727',
        overflowY: 'auto'
      }}>
        <h2 style={{ margin: '0 0 20px 0' }}>程序化材质测试</h2>
        
        {/* 缩略图预览 */}
        <div style={{ marginBottom: 20, textAlign: 'center' }}>
          <h4>缩略图预览</h4>
          <ProceduralMaterialThumbnail
            settings={materialSettings}
            size="large"
          />
        </div>
        
        {/* 参数控制 */}
        <CustomMaterialPanel
          defaultSettings={materialSettings}
          onChange={setMaterialSettings}
        />
        
        {/* 当前设置显示 */}
        <div style={{ 
          marginTop: 20, 
          padding: 10, 
          background: 'rgba(255,255,255,0.1)',
          borderRadius: 4,
          fontSize: 12
        }}>
          <h4>当前设置</h4>
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
            {JSON.stringify(materialSettings, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ProceduralMaterialTest;
