// 程序化材质着色器
import * as THREE from 'three';

// 导入 GLSL 噪声函数
const glslNoise = `
// 来自 glsl-noise 的高质量噪声函数
vec3 mod289(vec3 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}

vec4 mod289(vec4 x) {
  return x - floor(x * (1.0 / 289.0)) * 289.0;
}

vec4 permute(vec4 x) {
     return mod289(((x*34.0)+1.0)*x);
}

vec4 taylorInvSqrt(vec4 r) {
  return 1.79284291400159 - 0.85373472095314 * r;
}

float snoise(vec3 v) {
  const vec2  C = vec2(1.0/6.0, 1.0/3.0) ;
  const vec4  D = vec4(0.0, 0.5, 1.0, 2.0);

  vec3 i  = floor(v + dot(v, C.yyy) );
  vec3 x0 =   v - i + dot(i, C.xxx) ;

  vec3 g = step(x0.yzx, x0.xyz);
  vec3 l = 1.0 - g;
  vec3 i1 = min( g.xyz, l.zxy );
  vec3 i2 = max( g.xyz, l.zxy );

  vec3 x1 = x0 - i1 + C.xxx;
  vec3 x2 = x0 - i2 + C.yyy;
  vec3 x3 = x0 - D.yyy;

  i = mod289(i);
  vec4 p = permute( permute( permute(
             i.z + vec4(0.0, i1.z, i2.z, 1.0 ))
           + i.y + vec4(0.0, i1.y, i2.y, 1.0 ))
           + i.x + vec4(0.0, i1.x, i2.x, 1.0 ));

  float n_ = 0.142857142857;
  vec3  ns = n_ * D.wyz - D.xzx;

  vec4 j = p - 49.0 * floor(p * ns.z * ns.z);

  vec4 x_ = floor(j * ns.z);
  vec4 y_ = floor(j - 7.0 * x_ );

  vec4 x = x_ *ns.x + ns.yyyy;
  vec4 y = y_ *ns.x + ns.yyyy;
  vec4 h = 1.0 - abs(x) - abs(y);

  vec4 b0 = vec4( x.xy, y.xy );
  vec4 b1 = vec4( x.zw, y.zw );

  vec4 s0 = floor(b0)*2.0 + 1.0;
  vec4 s1 = floor(b1)*2.0 + 1.0;
  vec4 sh = -step(h, vec4(0.0));

  vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy ;
  vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww ;

  vec3 p0 = vec3(a0.xy,h.x);
  vec3 p1 = vec3(a0.zw,h.y);
  vec3 p2 = vec3(a1.xy,h.z);
  vec3 p3 = vec3(a1.zw,h.w);

  vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
  p0 *= norm.x;
  p1 *= norm.y;
  p2 *= norm.z;
  p3 *= norm.w;

  vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
  m = m * m;
  return 42.0 * dot( m*m, vec4( dot(p0,x0), dot(p1,x1),
                                dot(p2,x2), dot(p3,x3) ) );
}
`;

// 顶点着色器
export const vertexShader = `
  varying vec2 vUv;
  varying vec3 vNormal;
  varying vec3 vPosition;
  
  void main() {
    vUv = uv;
    vNormal = normalize(normalMatrix * normal);
    vPosition = position;
    
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

// 片段着色器
export const fragmentShader = `
  ${glslNoise}

  uniform vec3 baseColor;
  uniform float metalness;
  uniform float roughness;
  uniform float opacity;
  uniform float time;

  // 程序化纹理参数
  uniform bool useProceduralTexture;
  uniform int proceduralType; // 0: noise, 1: marble, 2: wood, 3: cellular, 4: fbm
  uniform float noiseScale;
  uniform float noiseIntensity;
  uniform int noiseOctaves;
  uniform float noiseFrequency;
  uniform float noiseAmplitude;
  uniform float animationSpeed;

  varying vec2 vUv;
  varying vec3 vNormal;
  varying vec3 vPosition;
  
  // 高质量 3D 噪声函数
  float noise3D(vec3 p) {
    return snoise(p);
  }

  // 分形噪声 (FBM)
  float fbm(vec3 p) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;

    for (int i = 0; i < 8; i++) {
      if (i >= noiseOctaves) break;
      value += amplitude * noise3D(p * frequency);
      frequency *= 2.0;
      amplitude *= 0.5;
    }
    return value;
  }
  
  // 大理石纹理
  float marble(vec3 p) {
    return sin((p.x + fbm(p * 4.0)) * 3.14159);
  }

  // 木纹纹理
  float wood(vec3 p) {
    float dist = sqrt(p.x * p.x + p.z * p.z);
    return sin(dist * 20.0 + fbm(p * 3.0) * 5.0);
  }

  // 细胞噪声 (简化版本)
  float cellular(vec3 p) {
    vec3 i = floor(p);
    vec3 f = fract(p);

    float min_dist = 1.0;

    for (int z = -1; z <= 1; z++) {
      for (int y = -1; y <= 1; y++) {
        for (int x = -1; x <= 1; x++) {
          vec3 neighbor = vec3(float(x), float(y), float(z));
          vec3 point = vec3(0.5) + neighbor;
          float dist = length(f - point);
          min_dist = min(min_dist, dist);
        }
      }
    }

    return min_dist;
  }
  
  // 获取程序化纹理值
  float getProceduralValue(vec2 uv, vec3 position) {
    vec3 p = vec3(uv * noiseScale, position.z * noiseScale) + time * animationSpeed;

    if (proceduralType == 0) {
      // 基础噪声
      return noise3D(p * noiseFrequency) * noiseAmplitude;
    } else if (proceduralType == 1) {
      // 大理石
      return marble(p * noiseFrequency) * noiseAmplitude;
    } else if (proceduralType == 2) {
      // 木纹
      return wood(p * noiseFrequency) * noiseAmplitude;
    } else if (proceduralType == 3) {
      // 细胞
      return cellular(p * noiseFrequency) * noiseAmplitude;
    } else if (proceduralType == 4) {
      // 分形噪声
      return fbm(p * noiseFrequency) * noiseAmplitude;
    }

    return 0.0;
  }
  
  void main() {
    vec3 color = baseColor;

    if (useProceduralTexture) {
      float proceduralValue = getProceduralValue(vUv, vPosition);
      proceduralValue = proceduralValue * noiseIntensity;

      // 将噪声值应用到颜色，创建更自然的混合效果
      vec3 noiseColor = color + vec3(proceduralValue);
      color = mix(color, noiseColor, abs(proceduralValue) * 0.5);
      color = clamp(color, 0.0, 1.0);
    }

    // 改进的光照计算
    vec3 normal = normalize(vNormal);
    vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
    vec3 viewDir = normalize(-vPosition);
    vec3 halfDir = normalize(lightDir + viewDir);

    float NdotL = max(dot(normal, lightDir), 0.0);
    float NdotH = max(dot(normal, halfDir), 0.0);

    // 环境光
    vec3 ambient = color * 0.2;

    // 漫反射
    vec3 diffuse = color * NdotL * 0.6;

    // 简单的镜面反射
    float specular = pow(NdotH, 32.0) * metalness * 0.3;
    vec3 specularColor = vec3(specular);

    vec3 finalColor = ambient + diffuse + specularColor;

    gl_FragColor = vec4(finalColor, opacity);
  }
`;

// 创建程序化材质
export function createProceduralMaterial(settings: {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  useProceduralTexture?: boolean;
  proceduralType?: 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm';
  noiseScale?: number;
  noiseIntensity?: number;
  noiseOctaves?: number;
  noiseFrequency?: number;
  noiseAmplitude?: number;
  animationSpeed?: number;
}) {
  const proceduralTypeMap = {
    'noise': 0,
    'marble': 1,
    'wood': 2,
    'cellular': 3,
    'fbm': 4
  };

  const uniforms = {
    baseColor: { value: new THREE.Color(settings.color) },
    metalness: { value: settings.metalness },
    roughness: { value: settings.roughness },
    opacity: { value: settings.opacity },
    time: { value: 0 },
    
    useProceduralTexture: { value: settings.useProceduralTexture || false },
    proceduralType: { value: proceduralTypeMap[settings.proceduralType || 'noise'] },
    noiseScale: { value: settings.noiseScale || 5.0 },
    noiseIntensity: { value: settings.noiseIntensity || 0.5 },
    noiseOctaves: { value: settings.noiseOctaves || 4 },
    noiseFrequency: { value: settings.noiseFrequency || 1.0 },
    noiseAmplitude: { value: settings.noiseAmplitude || 1.0 },
    animationSpeed: { value: settings.animationSpeed || 0.0 }
  };

  const material = new THREE.ShaderMaterial({
    uniforms,
    vertexShader,
    fragmentShader,
    transparent: settings.opacity < 1,
    side: THREE.FrontSide
  });

  return material;
}

// 更新材质参数
export function updateProceduralMaterial(
  material: THREE.ShaderMaterial, 
  settings: Partial<{
    color: string;
    metalness: number;
    roughness: number;
    opacity: number;
    useProceduralTexture: boolean;
    proceduralType: 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm';
    noiseScale: number;
    noiseIntensity: number;
    noiseOctaves: number;
    noiseFrequency: number;
    noiseAmplitude: number;
    animationSpeed: number;
  }>
) {
  const proceduralTypeMap = {
    'noise': 0,
    'marble': 1,
    'wood': 2,
    'cellular': 3,
    'fbm': 4
  };

  if (settings.color !== undefined) {
    material.uniforms.baseColor.value.setStyle(settings.color);
  }
  if (settings.metalness !== undefined) {
    material.uniforms.metalness.value = settings.metalness;
  }
  if (settings.roughness !== undefined) {
    material.uniforms.roughness.value = settings.roughness;
  }
  if (settings.opacity !== undefined) {
    material.uniforms.opacity.value = settings.opacity;
    material.transparent = settings.opacity < 1;
  }
  if (settings.useProceduralTexture !== undefined) {
    material.uniforms.useProceduralTexture.value = settings.useProceduralTexture;
  }
  if (settings.proceduralType !== undefined) {
    material.uniforms.proceduralType.value = proceduralTypeMap[settings.proceduralType];
  }
  if (settings.noiseScale !== undefined) {
    material.uniforms.noiseScale.value = settings.noiseScale;
  }
  if (settings.noiseIntensity !== undefined) {
    material.uniforms.noiseIntensity.value = settings.noiseIntensity;
  }
  if (settings.noiseOctaves !== undefined) {
    material.uniforms.noiseOctaves.value = settings.noiseOctaves;
  }
  if (settings.noiseFrequency !== undefined) {
    material.uniforms.noiseFrequency.value = settings.noiseFrequency;
  }
  if (settings.noiseAmplitude !== undefined) {
    material.uniforms.noiseAmplitude.value = settings.noiseAmplitude;
  }
  if (settings.animationSpeed !== undefined) {
    material.uniforms.animationSpeed.value = settings.animationSpeed;
  }
}
