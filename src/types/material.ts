// 材质相关的类型定义

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  // 程序化纹理设置
  useProceduralTexture?: boolean;
  proceduralType?: 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm';
  noiseScale?: number;
  noiseIntensity?: number;
  noiseOctaves?: number;
  noiseFrequency?: number;
  noiseAmplitude?: number;
  animationSpeed?: number;
}

export type ProceduralTextureType = 'noise' | 'marble' | 'wood' | 'cellular' | 'fbm';
