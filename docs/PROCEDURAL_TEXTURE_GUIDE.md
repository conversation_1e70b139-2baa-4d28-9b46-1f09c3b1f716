# 程序化纹理功能指南

## 概述

程序化纹理功能允许用户通过 Shader 实现各种程序化噪声效果，提供丰富的参数调节选项，创造出独特的材质效果。

## 功能特性

### 支持的纹理类型

1. **噪声 (Noise)** - 基础 Perlin 噪声效果
2. **大理石 (Marble)** - 大理石纹理效果
3. **木纹 (Wood)** - 木材纹理效果
4. **细胞 (Cellular)** - 细胞状噪声效果
5. **分形噪声 (FBM)** - 分形布朗运动噪声

### 可调节参数

- **缩放 (Scale)**: 控制纹理的整体大小 (0.1 - 20)
- **强度 (Intensity)**: 控制纹理效果的强度 (0 - 2)
- **细节层次 (Octaves)**: 控制噪声的细节层次 (1 - 8)
- **频率 (Frequency)**: 控制噪声的频率 (0.1 - 5)
- **振幅 (Amplitude)**: 控制噪声的振幅 (0.1 - 3)
- **动画速度 (Animation Speed)**: 控制纹理动画的速度 (0 - 2)

## 使用方法

### 1. 启用程序化纹理

1. 在渲染页面选择一个可编辑的材质（以 "Editable" 开头的材质）
2. 切换到"自定义"标签页
3. 开启"程序化纹理"开关

### 2. 选择纹理类型

在纹理类型下拉菜单中选择想要的效果：
- 噪声：适合创建随机纹理
- 大理石：适合创建石材效果
- 木纹：适合创建木材效果
- 细胞：适合创建有机纹理
- 分形噪声：适合创建复杂的自然纹理

### 3. 调节参数

使用滑块调节各项参数：
- **缩放**：数值越大，纹理越密集
- **强度**：数值越大，纹理效果越明显
- **细节层次**：数值越大，纹理细节越丰富
- **频率**：影响纹理的重复频率
- **振幅**：影响纹理的对比度
- **动画速度**：设置为 0 则静态，大于 0 则产生动画效果

### 4. 实时预览

在材质预览区域可以实时查看效果，所有参数调节都会立即反映在预览球体上。

## 技术实现

### Shader 架构

程序化纹理使用 Three.js 的 ShaderMaterial 实现：

- **顶点着色器**: 处理几何变换和 UV 坐标传递
- **片段着色器**: 实现各种噪声算法和纹理生成
- **Uniforms**: 传递可调节参数到着色器

### 噪声算法

实现了多种噪声算法：
- 基础随机噪声
- Perlin 噪声
- 分形布朗运动 (FBM)
- 细胞噪声 (Cellular/Worley)

### 性能优化

- 使用 GPU 着色器计算，性能优异
- 支持实时参数调节
- 动画效果基于时间 uniform，避免重复计算

## 最佳实践

### 参数调节建议

1. **从基础开始**: 先选择合适的纹理类型，再调节参数
2. **逐步调节**: 一次只调节一个参数，观察效果变化
3. **保存设置**: 找到满意的效果后，可以应用到模型上

### 性能考虑

1. **动画速度**: 非必要时建议设置为 0，避免不必要的 GPU 计算
2. **细节层次**: 过高的 Octaves 值会影响性能
3. **复杂度**: 分形噪声比基础噪声计算量更大

### 创意应用

1. **金属表面**: 使用低强度噪声模拟金属表面的微观纹理
2. **石材效果**: 使用大理石纹理配合适当的颜色
3. **有机材质**: 使用细胞噪声创建皮肤、布料等有机材质
4. **动态效果**: 使用动画速度创建流动、闪烁等动态效果

## 故障排除

### 常见问题

1. **纹理不显示**: 确保已启用程序化纹理开关
2. **效果不明显**: 增加强度参数
3. **纹理太密集**: 减小缩放参数
4. **性能问题**: 降低细节层次或关闭动画

### 兼容性

- 支持所有现代浏览器
- 需要 WebGL 支持
- 移动设备性能可能有限制

## 测试和调试

### 测试页面

访问 `/test/procedural` 可以打开程序化材质测试页面，该页面提供：

1. **实时 3D 预览**: 在球体上实时查看材质效果
2. **参数控制面板**: 调节所有程序化纹理参数
3. **预设配置**: 快速切换到预定义的材质效果
4. **缩略图预览**: 查看材质在缩略图中的显示效果
5. **设置导出**: 查看当前材质的完整参数配置

### 调试技巧

1. **参数范围**: 每个参数都有合理的取值范围，超出范围可能产生意外效果
2. **性能监控**: 使用浏览器开发者工具监控 GPU 使用情况
3. **着色器错误**: 检查浏览器控制台是否有 WebGL 错误信息

## 扩展开发

### 添加新的纹理类型

1. 在 `proceduralMaterial.ts` 中添加新的噪声函数：
```glsl
float myCustomNoise(vec3 p) {
  // 实现自定义噪声算法
  return snoise(p * 2.0) * 0.5 + 0.5;
}
```

2. 更新 `getProceduralValue` 函数添加新的类型分支
3. 在 `MaterialSettings` 接口中更新 `proceduralType` 类型
4. 在 UI 组件中添加新的选项

### 自定义参数

可以通过修改 `MaterialSettings` 接口添加新的可调节参数：

```typescript
export interface MaterialSettings {
  // 现有参数...

  // 新增自定义参数
  customParam1?: number;
  customParam2?: boolean;
}
```

然后在着色器中添加对应的 uniform 和处理逻辑。

### 高级功能

1. **纹理混合**: 支持多种纹理类型的混合
2. **颜色映射**: 基于噪声值应用不同的颜色
3. **法线贴图**: 使用噪声生成法线贴图效果
4. **位移映射**: 使用噪声进行几何位移

## 版本历史

### v1.0.0
- 实现基础程序化纹理功能
- 支持 5 种纹理类型
- 提供完整的参数调节界面
- 集成到主渲染系统

---

通过程序化纹理功能，用户可以创造出无限可能的材质效果，为 3D 模型渲染带来更多创意空间。该功能基于现代 GPU 着色器技术，提供高性能的实时渲染体验。
