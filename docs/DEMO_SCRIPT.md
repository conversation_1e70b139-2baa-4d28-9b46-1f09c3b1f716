# 程序化纹理功能演示脚本

## 演示目标
展示新实现的程序化纹理功能，包括各种噪声效果和参数调节能力。

## 演示步骤

### 1. 功能概述 (30秒)
- 打开主渲染页面 `http://localhost:5174/render`
- 简要介绍程序化纹理是什么：使用 Shader 实现的程序化噪声效果
- 说明优势：无需贴图文件、无限分辨率、参数化调节

### 2. 基础使用流程 (60秒)

#### 2.1 选择可编辑材质
- 加载一个包含可编辑材质的 3D 模型
- 点击左侧材质列表中以 "Editable" 开头的材质
- 解释只有可编辑材质才能应用程序化纹理

#### 2.2 启用程序化纹理
- 切换到"自定义"标签页
- 开启"程序化纹理"开关
- 观察材质预览球体的变化

#### 2.3 选择纹理类型
- 演示 5 种不同的纹理类型：
  - **噪声**: 基础随机纹理效果
  - **大理石**: 大理石纹理，适合石材
  - **木纹**: 木材纹理，展示同心圆效果
  - **细胞**: 细胞状纹理，有机感强
  - **分形噪声**: 复杂的多层次纹理

### 3. 参数调节演示 (90秒)

#### 3.1 基础参数
- **缩放 (Scale)**: 从 1 调到 20，展示纹理密度变化
- **强度 (Intensity)**: 从 0 调到 2，展示效果强弱
- **透明度**: 展示材质透明效果

#### 3.2 高级参数
- **细节层次 (Octaves)**: 从 1 调到 8，展示细节丰富度
- **频率 (Frequency)**: 调节纹理重复频率
- **振幅 (Amplitude)**: 调节纹理对比度

#### 3.3 动画效果
- **动画速度**: 从 0 调到 2，展示动态纹理效果
- 说明动画基于时间，创造流动感

### 4. 实际应用场景 (60秒)

#### 4.1 金属表面
- 选择金属色彩 (#C0C0C0)
- 使用低强度噪声 (0.2)
- 高金属度 (0.8)，低粗糙度 (0.3)
- 展示金属表面的微观纹理效果

#### 4.2 石材效果
- 选择大理石纹理类型
- 使用石材色彩 (#F5F5DC)
- 中等强度 (0.6)，适当缩放 (3.0)
- 展示自然石材纹理

#### 4.3 有机材质
- 选择细胞纹理类型
- 使用皮肤色彩 (#FDBCB4)
- 高缩放值 (10.0)，中等强度 (0.5)
- 展示皮肤或布料等有机材质

### 5. 测试页面演示 (45秒)
- 打开测试页面 `http://localhost:5174/test/procedural`
- 展示预设配置按钮的快速切换功能
- 演示实时参数调节和预览
- 展示缩略图预览功能
- 查看当前设置的 JSON 配置

### 6. 技术亮点说明 (30秒)
- **GPU 加速**: 基于 WebGL Shader，性能优异
- **实时渲染**: 所有参数调节都是实时的
- **高质量噪声**: 使用业界标准的 Perlin 噪声算法
- **无缝集成**: 与现有材质系统完美融合

## 演示要点

### 视觉效果重点
1. **对比展示**: 开启/关闭程序化纹理的对比
2. **参数变化**: 实时调节参数时的平滑过渡
3. **多样性**: 不同纹理类型的独特效果
4. **质量**: 高分辨率、无像素化的纹理效果

### 技术特性重点
1. **性能**: 流畅的实时渲染
2. **兼容性**: 与现有系统的无缝集成
3. **扩展性**: 易于添加新的纹理类型
4. **用户友好**: 直观的参数控制界面

### 实用价值重点
1. **创意自由**: 无限的材质变化可能
2. **效率提升**: 无需外部贴图文件
3. **动态效果**: 支持动画纹理
4. **专业品质**: 达到专业渲染软件的效果

## 常见问题解答

### Q: 程序化纹理与传统贴图有什么区别？
A: 程序化纹理通过算法生成，无分辨率限制，文件体积小，可参数化调节。

### Q: 性能如何？
A: 基于 GPU Shader 计算，性能优异，支持实时调节。

### Q: 是否支持所有浏览器？
A: 支持所有现代浏览器，需要 WebGL 支持。

### Q: 可以保存设置吗？
A: 当前版本支持实时应用，后续可扩展保存功能。

## 演示总结
程序化纹理功能为 3D 材质渲染带来了革命性的改进，提供了前所未有的创意自由度和技术先进性。通过简单的参数调节，用户可以创造出专业级的材质效果，大大提升了产品的竞争力和用户体验。
