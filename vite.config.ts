import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  },
  build: {
    chunkSizeWarningLimit: 1000, // 将警告限制提高到 1000kb
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 React 相关的库打包在一起
          'react-vendor': ['react', 'react-dom'],
          // 将其他大型第三方库分开打包
          'three-vendor': ['three', 'three-stdlib'],
          'other-vendor': ['hls.js', 'detect-gpu']
        }
      }
    }
  }
})